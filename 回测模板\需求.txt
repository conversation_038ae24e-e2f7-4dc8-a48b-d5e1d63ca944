参考  backtester.py  和 dynamic_gap_detector.py 给backtestv8.py 加上回测买入信号的功能 1、文件头定义买入金额
    总资产配置  买入信号之前是S/A/B 级的现在变成 绝对龙头 单股龙头 双龙头 三足鼎立 例如
  绝对龙头这个股票，发现就买入
    ，在文件头定义买入的信号类型， 绝对龙头 单股龙头 双龙头 三足鼎立 ，可以单个也可以全部，
    2、文件头定义不同的信号类型的单个买入进行，例如绝对龙头 10万
    也就是发现这种类型的股票就买入10万股票，所有类型都可以定义，例如绝对龙头 单股龙头
    是10万一个股票，双龙头是7万一个股票，三足鼎立是三万一个股票
  4，文件头可以定义买入的概念和行业的名次，例如只买入1-3名的概念或行业，如果出现【第10名】家电行业 - 总流入: 0.94亿
     【第9名】风电设备 - 总流入: 1.34亿 不是前三名的行业或概念则忽略买入       3、 参考backtester.py
  买入后不能再重复购买
    4、所有回测生成完成后，需要有一个整天的买入和盈亏计算，参考backtester.py  和
  dynamic_gap_detector.py是怎么实现的