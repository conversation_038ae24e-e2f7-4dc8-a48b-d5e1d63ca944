#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试火种快速通道和健康度模型集成
"""

import pandas as pd
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dynamic_gap_detector import (
    _identify_kindling_signal, 
    calculate_battlefield_health,
    generate_tiered_watchlist_and_report
)

def quick_test():
    """快速验证两个关键修复"""
    print("🚀 快速验证火种快速通道 + 健康度模型...")
    
    # 创建测试数据
    stock_flow_data = pd.DataFrame({
        '名称': ['捷佳伟创', '光伏股票A'],
        '代码': ['300724', '601012'],
        '今日主力净流入-净额': [500000000, 200000000],
        '今日涨跌幅': [10.0, 5.0]
    })
    
    all_sectors_df = pd.DataFrame({
        '名称': ['光伏设备'],
        '今日主力净流入-净额': [350000000],
        'type': ['概念'],
        'max_consecutive': [2],
        'limit_up_count': [3]
    })
    
    limit_up_stocks = [
        {'名称': '捷佳伟创', '连板数': 3, '股票类型': '20CM'}
    ]
    
    # 测试火种识别
    kindling_candidates = _identify_kindling_signal(stock_flow_data, limit_up_stocks)
    print(f"🔥 火种候选: {[k['stock_name'] for k in kindling_candidates]}")
    
    # 测试健康度评估
    health_results = calculate_battlefield_health(all_sectors_df, limit_up_stocks)
    print(f"🏥 健康度评估: {list(health_results.keys())}")
    
    # 测试集成决策
    try:
        tier1_stocks, tier2_stocks, tier3_stocks, report = generate_tiered_watchlist_and_report(
            all_sectors_df_sorted=all_sectors_df,
            stock_flow_data=stock_flow_data,
            limit_up_stocks=limit_up_stocks,
            kindling_candidates=kindling_candidates
        )
        
        tier1_names = [stock.get('stock_name', '') for stock in tier1_stocks]
        print(f"✅ 第一梯队: {tier1_names}")
        
        # 验证火种快速通道
        if '捷佳伟创' in tier1_names:
            print("✅ 火种快速通道正常工作")
        else:
            print("❌ 火种快速通道失败")
        
        # 验证健康度模型
        if "回退到三维筛选法则" not in report:
            print("✅ 健康度模型正常工作")
        else:
            print("❌ 健康度模型失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    quick_test()
