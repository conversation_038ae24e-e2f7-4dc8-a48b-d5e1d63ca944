#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票收盘价缓存模块
使用akshare接口获取全市场收盘价并进行缓存
"""

import pandas as pd
import os
import json
from datetime import datetime
import akshare as ak

class StockPriceCache:
    """股票收盘价缓存管理器"""
    
    def __init__(self, cache_dir="cache/stock_prices"):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录路径
        """
        self.cache_dir = cache_dir
        self._ensure_cache_dir()
    
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir, exist_ok=True)
    
    def _get_cache_file_path(self, date_str):
        """获取指定日期的缓存文件路径"""
        return os.path.join(self.cache_dir, f"stock_prices_{date_str}.json")
    
    def _is_cache_valid(self, date_str):
        """检查缓存是否存在且有效"""
        cache_file = self._get_cache_file_path(date_str)
        return os.path.exists(cache_file)
    
    def _load_cache(self, date_str):
        """从缓存文件加载价格数据"""
        cache_file = self._get_cache_file_path(date_str)
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"[INFO] 从缓存加载 {date_str} 的股票价格数据，共 {len(data)} 只股票")
                return data
        except Exception as e:
            print(f"[ERROR] 加载缓存失败: {e}")
            return {}
    
    def _save_cache(self, date_str, price_data):
        """保存价格数据到缓存文件"""
        cache_file = self._get_cache_file_path(date_str)
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(price_data, f, ensure_ascii=False, indent=2)
                print(f"[INFO] 已缓存 {date_str} 的股票价格数据到 {cache_file}")
        except Exception as e:
            print(f"[ERROR] 保存缓存失败: {e}")
    
    def _fetch_market_prices(self):
        """从akshare获取全市场股票价格数据"""
        try:
            print("[INFO] 正在从akshare获取全市场股票数据...")
            
            # 获取沪深京A股实时行情数据
            df = ak.stock_zh_a_spot_em()
            
            if df.empty:
                print("[ERROR] 未获取到股票数据")
                return {}
            
            print(f"[INFO] 成功获取 {len(df)} 只股票的行情数据")
            
            # 转换为字典格式 {股票名称: 最新价, 股票代码: 最新价}
            price_data = {}
            
            for _, row in df.iterrows():
                stock_name = row.get('名称', '')
                stock_code = row.get('代码', '')
                latest_price = row.get('最新价', 0)
                
                if stock_name and latest_price > 0:
                    price_data[stock_name] = float(latest_price)
                
                if stock_code and latest_price > 0:
                    price_data[stock_code] = float(latest_price)
            
            print(f"[INFO] 处理完成，共 {len(price_data)} 条价格记录")
            return price_data
            
        except Exception as e:
            print(f"[ERROR] 获取市场数据失败: {e}")
            return {}
    
    def get_stock_prices(self, date_str):
        """
        获取指定日期的股票价格数据
        
        Args:
            date_str: 日期字符串，格式如 '2025-08-01'
            
        Returns:
            dict: {股票名称/代码: 价格} 的字典
        """
        # 检查缓存
        if self._is_cache_valid(date_str):
            return self._load_cache(date_str)
        
        # 缓存不存在，从akshare获取数据
        print(f"[INFO] 缓存不存在，正在获取 {date_str} 的股票价格数据...")
        price_data = self._fetch_market_prices()
        
        if price_data:
            # 保存到缓存
            self._save_cache(date_str, price_data)
        
        return price_data
    
    def get_stock_price(self, stock_identifier, date_str):
        """
        获取指定股票在指定日期的价格
        
        Args:
            stock_identifier: 股票名称或代码
            date_str: 日期字符串
            
        Returns:
            float: 股票价格，如果未找到返回0
        """
        price_data = self.get_stock_prices(date_str)
        return price_data.get(stock_identifier, 0)
    
    def clear_cache(self, date_str=None):
        """
        清理缓存
        
        Args:
            date_str: 指定日期的缓存，如果为None则清理所有缓存
        """
        if date_str:
            cache_file = self._get_cache_file_path(date_str)
            if os.path.exists(cache_file):
                os.remove(cache_file)
                print(f"[INFO] 已清理 {date_str} 的缓存")
        else:
            # 清理所有缓存文件
            for file in os.listdir(self.cache_dir):
                if file.startswith('stock_prices_') and file.endswith('.json'):
                    os.remove(os.path.join(self.cache_dir, file))
            print("[INFO] 已清理所有价格缓存")


def test_price_cache():
    """测试价格缓存功能"""
    cache = StockPriceCache()
    
    # 测试获取价格数据
    test_date = "2025-08-01"
    prices = cache.get_stock_prices(test_date)
    
    if prices:
        print(f"\n测试结果:")
        print(f"获取到 {len(prices)} 条价格数据")
        
        # 测试几个常见股票
        test_stocks = ['招商银行', '农业银行', '平安银行', '000001', '600036']
        for stock in test_stocks:
            price = cache.get_stock_price(stock, test_date)
            print(f"{stock}: {price}")
    else:
        print("未获取到价格数据")


if __name__ == "__main__":
    test_price_cache()
