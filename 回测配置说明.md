# 回测模块配置说明

## 📋 概述

回测模块现已支持在 `backtester.py` 文件头进行灵活配置，您可以轻松调整不同级别信号的买入金额和仓位比例。

## 🎯 配置参数

### 基础配置
```python
# 总资产配置
TOTAL_CAPITAL = 2000000  # 总资产200万

# 风险控制参数
MIN_CASH_RESERVE = 50000            # 最低现金储备: 5万
MAX_SINGLE_POSITION = 1000000       # 单个股票最大持仓金额: 100万
MIN_TRADE_AMOUNT = 10000            # 最小交易金额: 1万

# 信号级别过滤配置
ENABLED_SIGNAL_LEVELS = ['S', 'A']  # 允许交易的信号级别
                                    # 默认只交易S级和A级信号，B级信号将被跳过
                                    # 设置为 'all' 或 ['S', 'A', 'B'] 可交易所有级别
```

### 信号级别配置

#### S级信号 (最高优先级)
```python
S_LEVEL_SINGLE_AMOUNT = 100000      # S级单个股票买入金额: 10万
S_LEVEL_POSITION_RATIO = 0.50       # S级信号仓位比例: 50%
```

#### A级信号 (中等优先级)
```python
A_LEVEL_SINGLE_AMOUNT = 70000       # A级单个股票买入金额: 7万
A_LEVEL_POSITION_RATIO = 0.35       # A级信号仓位比例: 35%
```

#### B级信号 (低优先级)
```python
B_LEVEL_SINGLE_AMOUNT = 30000       # B级单个股票买入金额: 3万
B_LEVEL_POSITION_RATIO = 0.15       # B级信号仓位比例: 15%
```

## 🔧 如何修改配置

### 1. 打开配置文件
编辑 `backtester.py` 文件，找到文件头的配置区域：

```python
# ==================== 回测配置参数 ====================
```

### 2. 修改买入金额
例如，将S级信号的买入金额改为15万：

```python
S_LEVEL_SINGLE_AMOUNT = 150000      # S级单个股票买入金额: 15万
```

### 3. 修改仓位比例
例如，调整各级别的仓位比例：

```python
S_LEVEL_POSITION_RATIO = 0.60       # S级信号仓位比例: 60%
A_LEVEL_POSITION_RATIO = 0.30       # A级信号仓位比例: 30%
B_LEVEL_POSITION_RATIO = 0.10       # B级信号仓位比例: 10%
```

### 4. 修改信号级别过滤
例如，只交易S级信号：

```python
ENABLED_SIGNAL_LEVELS = ['S']       # 只交易S级信号
```

或者交易所有级别：

```python
ENABLED_SIGNAL_LEVELS = 'all'       # 交易所有级别
# 或者
ENABLED_SIGNAL_LEVELS = ['S', 'A', 'B']  # 交易所有级别
```

## 📊 当前配置效果

基于当前配置（总资产200万）：

| 信号级别 | 单个买入金额 | 仓位比例 | 理论最大仓位 | 可买股票数 |
|---------|-------------|---------|-------------|-----------|
| S级     | 10万        | 50%     | 100万       | 10只      |
| A级     | 7万         | 35%     | 70万        | 10只      |
| B级     | 3万         | 15%     | 30万        | 10只      |

## 🎯 信号触发条件

### S级信号 (最高优先级)
- **条件1**: 三者共振
  - 盘口突袭信号 + 断层龙头确认 + 主战场确认
- **条件2**: 第一梯队股票

### A级信号 (中等优先级)
- **条件1**: 第二梯队股票
- **条件2**: 进入射程的突破信号 (★★☆评级)

### B级信号 (低优先级)
- **条件**: 新王蓄力板块的资金龙头

## 🛡️ 风险控制机制

### 1. 现金管理
- **最低现金储备**: 始终保留5万现金
- **交易前检查**: 确保有足够现金进行交易
- **现金不足保护**: 自动跳过无法执行的交易

### 2. 持仓控制
- **重复买入保护**: 同一股票只能买入一次
- **单个持仓限制**: 单只股票最大持仓100万
- **最小交易金额**: 低于1万的交易自动跳过

### 3. 信号级别过滤
- **级别过滤**: 只执行启用级别的信号，禁用级别自动跳过
- **灵活配置**: 可根据市场情况调整启用的信号级别
- **默认保守**: 默认禁用B级信号，降低交易频率

### 4. 股数计算
- **100股整数倍**: 所有买入股数向下取整到100股
- **价格保护**: 股价过高导致股数不足时自动跳过

## 💡 配置建议

### 保守型配置
```python
S_LEVEL_SINGLE_AMOUNT = 80000       # 8万
A_LEVEL_SINGLE_AMOUNT = 50000       # 5万
B_LEVEL_SINGLE_AMOUNT = 20000       # 2万
```

### 激进型配置
```python
S_LEVEL_SINGLE_AMOUNT = 150000      # 15万
A_LEVEL_SINGLE_AMOUNT = 100000      # 10万
B_LEVEL_SINGLE_AMOUNT = 50000       # 5万
```

### 均衡型配置 (当前默认)
```python
S_LEVEL_SINGLE_AMOUNT = 100000      # 10万
A_LEVEL_SINGLE_AMOUNT = 70000       # 7万
B_LEVEL_SINGLE_AMOUNT = 30000       # 3万
```

## 🚀 使用方法

### 1. 修改配置
在 `backtester.py` 文件头修改相应参数

### 2. 运行回测
```bash
python dynamic_gap_detector.py
```

### 3. 测试配置
```bash
python test_signal_level_trading.py      # 测试基础交易功能
python test_signal_level_filter.py       # 测试信号级别过滤功能
```

## 📈 配置优势

### ✅ 统一管理
- 所有配置参数集中在文件头
- 一目了然，易于理解和修改

### ✅ 灵活调整
- 支持独立调整每个级别的买入金额
- 支持调整仓位比例和风险参数

### ✅ 安全可靠
- 内置多重风险控制机制
- 自动检查和保护机制
- 信号级别过滤保护

### ✅ 实时生效
- 修改配置后立即生效
- 无需重新编译或复杂设置

## 🔍 监控和调试

### 交易日志
系统会实时显示：
- 交易执行结果
- 持仓状态变化
- 现金余额变化
- 风险控制触发情况

### 测试验证
使用测试脚本验证配置效果：
- 基础功能测试
- 风险控制测试
- 配置参数验证

## 📝 注意事项

1. **配置合理性**: 确保各级别买入金额符合实际需求
2. **现金储备**: 保持足够的现金储备应对市场变化
3. **仓位控制**: 避免单一股票或级别过度集中
4. **测试验证**: 修改配置后建议先运行测试脚本验证

## 🎉 总结

新的配置化回测模块提供了：
- **高度灵活性**: 可根据需求调整各项参数
- **严格风控**: 多重安全机制保护资金安全
- **简单易用**: 修改配置即可生效，无需复杂操作
- **专业级功能**: 支持多级别信号和精细化仓位管理

现在您可以根据自己的交易策略和风险偏好，灵活调整回测参数，获得更符合实际需求的回测结果！
