#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dynamic_gap_detector.py 的 datetime 修复
"""

import sys
import os

def test_datetime_import():
    """测试 datetime 导入是否正常"""
    try:
        # 模拟 dynamic_gap_detector.py 中的导入
        from datetime import datetime
        
        # 测试基本的 datetime 操作
        test_date = "20250804"
        backtest_date = datetime.strptime(test_date, '%Y%m%d').strftime('%Y-%m-%d')
        print(f"✅ datetime 导入和使用正常: {test_date} -> {backtest_date}")
        
        # 测试时间解析
        ts_str = "093000"
        current_sim_time = datetime.strptime(ts_str, '%H%M%S').time()
        print(f"✅ 时间解析正常: {ts_str} -> {current_sim_time}")
        
        # 测试时间比较
        start_time = datetime.strptime("09:30", "%H:%M").time()
        end_time = datetime.strptime("11:30", "%H:%M").time()
        is_in_range = start_time <= current_sim_time <= end_time
        print(f"✅ 时间比较正常: {current_sim_time} 在交易时间内: {is_in_range}")
        
        return True
        
    except Exception as e:
        print(f"❌ datetime 测试失败: {e}")
        return False

def test_dynamic_gap_import():
    """测试 dynamic_gap_detector 模块导入"""
    try:
        # 尝试导入模块（不执行主函数）
        import dynamic_gap_detector
        print("✅ dynamic_gap_detector 模块导入成功")
        
        # 检查关键函数是否存在
        if hasattr(dynamic_gap_detector, 'run_gap_analysis_backtest'):
            print("✅ 找到 run_gap_analysis_backtest 函数")
        else:
            print("❌ 未找到 run_gap_analysis_backtest 函数")
            
        return True
        
    except Exception as e:
        print(f"❌ dynamic_gap_detector 导入失败: {e}")
        return False

def test_price_cache_integration():
    """测试价格缓存集成"""
    try:
        from stock_price_cache import StockPriceCache
        
        # 测试创建价格缓存实例
        price_cache = StockPriceCache()
        print("✅ StockPriceCache 实例创建成功")
        
        # 测试日期格式处理
        BACKTEST_DATE = "2025-08-04"  # 模拟配置
        
        if '-' in BACKTEST_DATE:
            backtest_date = BACKTEST_DATE
        else:
            from datetime import datetime
            backtest_date = datetime.strptime(BACKTEST_DATE, '%Y%m%d').strftime('%Y-%m-%d')
            
        print(f"✅ 日期格式处理正常: {BACKTEST_DATE} -> {backtest_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ 价格缓存集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试 dynamic_gap_detector.py datetime 修复")
    print("=" * 60)
    
    # 测试1: datetime 基础功能
    print("1. 测试 datetime 基础功能:")
    test1_result = test_datetime_import()
    
    # 测试2: 模块导入
    print("\n2. 测试模块导入:")
    test2_result = test_dynamic_gap_import()
    
    # 测试3: 价格缓存集成
    print("\n3. 测试价格缓存集成:")
    test3_result = test_price_cache_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"  datetime 基础功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  模块导入: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"  价格缓存集成: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 dynamic_gap_detector.py datetime 修复成功！")
        print("现在可以正常运行，不会再出现 UnboundLocalError 错误。")
    else:
        print("\n⚠️ 部分功能需要进一步检查。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
